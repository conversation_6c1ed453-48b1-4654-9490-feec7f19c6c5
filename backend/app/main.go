package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"syscall"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
)

// Student represents a student record
type Student struct {
	ID           int32     `json:"id"`
	Name         string    `json:"name"`
	Email        string    `json:"email"`
	DateOfBirth  string    `json:"date_of_birth"`
	Gender       string    `json:"gender"`
	Category     string    `json:"category"`
	MobileNumber string    `json:"mobile_number"`
	FatherName   string    `json:"father_name"`
	MotherName   string    `json:"mother_name"`
	Pincode      string    `json:"pincode"`
	State        string    `json:"state"`
	AadharNumber string    `json:"aadhar_number"`
	AbcID        string    `json:"abc_id"`
	AadharMobile string    `json:"aadhar_mobile"`
	Status       string    `json:"status"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// PageData represents data passed to templates
type PageData struct {
	Title           string
	IsAuthenticated bool
	Student         *Student
	Error           string
	Success         string
}

// Database holds the database connection
type Database struct {
	db *sql.DB
}

// NewDatabase creates a new database instance
func NewDatabase(db *sql.DB) *Database {
	return &Database{db: db}
}

// CreateStudent creates a new student record
func (d *Database) CreateStudent(ctx context.Context, student *Student) (*Student, error) {
	query := `
		INSERT INTO students (name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13)
		RETURNING id, name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
			status, created_at, updated_at`

	var result Student
	var dob time.Time
	err := d.db.QueryRowContext(ctx, query,
		student.Name, student.Email, student.DateOfBirth, student.Gender, student.Category,
		student.MobileNumber, student.FatherName, student.MotherName, student.Pincode,
		student.State, student.AadharNumber, student.AbcID, student.AadharMobile,
	).Scan(
		&result.ID, &result.Name, &result.Email, &dob, &result.Gender, &result.Category,
		&result.MobileNumber, &result.FatherName, &result.MotherName, &result.Pincode,
		&result.State, &result.AadharNumber, &result.AbcID, &result.AadharMobile,
		&result.Status, &result.CreatedAt, &result.UpdatedAt,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create student: %w", err)
	}

	result.DateOfBirth = dob.Format("2006-01-02")
	return &result, nil
}

// GetStudentByEmail retrieves a student by email
func (d *Database) GetStudentByEmail(ctx context.Context, email string) (*Student, error) {
	query := `
		SELECT id, name, email, date_of_birth, gender, category, mobile_number,
			father_name, mother_name, pincode, state, aadhar_number, abc_id, aadhar_mobile,
			status, created_at, updated_at
		FROM students WHERE email = $1`

	var student Student
	var dob time.Time
	err := d.db.QueryRowContext(ctx, query, email).Scan(
		&student.ID, &student.Name, &student.Email, &dob, &student.Gender, &student.Category,
		&student.MobileNumber, &student.FatherName, &student.MotherName, &student.Pincode,
		&student.State, &student.AadharNumber, &student.AbcID, &student.AadharMobile,
		&student.Status, &student.CreatedAt, &student.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("student not found")
		}
		return nil, fmt.Errorf("failed to get student: %w", err)
	}

	student.DateOfBirth = dob.Format("2006-01-02")
	return &student, nil
}

// Server holds the application state
type Server struct {
	db        *Database
	templates map[string]*template.Template
}

// NewServer creates a new server instance
func NewServer(db *sql.DB) (*Server, error) {
	server := &Server{
		db:        NewDatabase(db),
		templates: make(map[string]*template.Template),
	}

	// Load templates
	if err := server.loadTemplates(); err != nil {
		return nil, fmt.Errorf("failed to load templates: %w", err)
	}

	return server, nil
}

// loadTemplates loads all HTML templates
func (s *Server) loadTemplates() error {
	templateDir := "templates"

	// Template files to load
	templateFiles := map[string][]string{
		"index": {
			templateDir + "/base.html",
			templateDir + "/index.html",
			templateDir + "/components/navbar.html",
		},
		"form": {
			templateDir + "/base.html",
			templateDir + "/form.html",
			templateDir + "/components/navbar.html",
		},
	}

	for name, files := range templateFiles {
		tmpl, err := template.ParseFiles(files...)
		if err != nil {
			return fmt.Errorf("failed to parse template %s: %w", name, err)
		}
		s.templates[name] = tmpl
	}

	log.Printf("✅ Loaded %d templates successfully", len(s.templates))
	return nil
}

// renderTemplate renders a template with data
func (s *Server) renderTemplate(w http.ResponseWriter, templateName string, data PageData) {
	tmpl, exists := s.templates[templateName]
	if !exists {
		log.Printf("Template %s not found", templateName)
		http.Error(w, "Template not found", http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	if err := tmpl.ExecuteTemplate(w, "base.html", data); err != nil {
		log.Printf("Failed to execute template %s: %v", templateName, err)
		http.Error(w, "Failed to render template", http.StatusInternalServerError)
		return
	}
}

// HTTP Handlers

// indexHandler serves the home page
func (s *Server) indexHandler(w http.ResponseWriter, r *http.Request) {
	data := PageData{
		Title:           "University Information Management System",
		IsAuthenticated: false,
	}
	s.renderTemplate(w, "index", data)
}

// formHandler handles student registration form
func (s *Server) formHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		data := PageData{
			Title:           "Student Registration",
			IsAuthenticated: false,
		}
		s.renderTemplate(w, "form", data)
		return
	}

	if r.Method == "POST" {
		// Parse form data
		if err := r.ParseForm(); err != nil {
			http.Error(w, "Failed to parse form", http.StatusBadRequest)
			return
		}

		student := &Student{
			Name:         r.FormValue("name"),
			Email:        r.FormValue("email"),
			DateOfBirth:  r.FormValue("date_of_birth"),
			Gender:       r.FormValue("gender"),
			Category:     r.FormValue("category"),
			MobileNumber: r.FormValue("mobile_number"),
			FatherName:   r.FormValue("father_name"),
			MotherName:   r.FormValue("mother_name"),
			Pincode:      r.FormValue("pincode"),
			State:        r.FormValue("state"),
			AadharNumber: r.FormValue("aadhar_number"),
			AbcID:        r.FormValue("abc_id"),
			AadharMobile: r.FormValue("aadhar_mobile"),
		}

		// Create student in database
		createdStudent, err := s.db.CreateStudent(r.Context(), student)
		if err != nil {
			log.Printf("Failed to create student: %v", err)
			data := PageData{
				Title:           "Student Registration",
				IsAuthenticated: false,
				Error:           "Failed to register student. Please try again.",
			}
			s.renderTemplate(w, "form", data)
			return
		}

		// Success response
		data := PageData{
			Title:           "Student Registration",
			IsAuthenticated: false,
			Student:         createdStudent,
			Success:         "Student registered successfully!",
		}
		s.renderTemplate(w, "form", data)
	}
}

// healthHandler provides health check endpoint
func (s *Server) healthHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status": "healthy",
		"time":   time.Now().Format(time.RFC3339),
	})
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

func main() {
	// Get database connection parameters from environment variables
	dbHost := getEnv("DB_HOST", "localhost")
	dbPort := getEnv("DB_PORT", "5432")
	dbUser := getEnv("POSTGRES_USER", "balena")
	dbPassword := getEnv("POSTGRES_PASSWORD", "test")
	dbName := getEnv("DB_NAME", "balena")

	// URL-encode the password to avoid issues with special characters
	encodedPassword := url.QueryEscape(dbPassword)

	// Build connection string
	connStr := fmt.Sprintf("postgres://%s:%s@%s:%s/%s?sslmode=disable",
		dbUser, encodedPassword, dbHost, dbPort, dbName)

	// Connect to the database
	db, err := sql.Open("postgres", connStr)
	if err != nil {
		log.Fatalf("Error connecting to the database: %v", err)
	}
	defer db.Close()

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	if err := db.Ping(); err != nil {
		log.Fatalf("Could not ping the database: %v", err)
	}

	log.Println("✅ Connected to database successfully")

	// Create server instance
	server, err := NewServer(db)
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// Setup HTTP routes
	mux := http.NewServeMux()

	// Static file serving
	mux.Handle("/assets/", http.StripPrefix("/assets/", http.FileServer(http.Dir("assets"))))

	// Routes
	mux.HandleFunc("/", server.indexHandler)
	mux.HandleFunc("/form", server.formHandler)
	mux.HandleFunc("/health", server.healthHandler)

	// Create HTTP server
	port := getEnv("PORT", "8080")
	httpServer := &http.Server{
		Addr:         ":" + port,
		Handler:      mux,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("🚀 Server starting on port %s", port)
		if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP server error: %v", err)
		}
	}()

	// Wait for interrupt signal to gracefully shutdown the server
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Create a deadline to wait for
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Attempt graceful shutdown
	if err := httpServer.Shutdown(ctx); err != nil {
		log.Fatalf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exited")
}
